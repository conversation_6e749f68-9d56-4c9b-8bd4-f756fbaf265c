"""
Epidemiology Research System - Streamlit Web Application

This is the main entry point for the Streamlit web application that replaces
the CLI interface with an interactive user experience for epidemiology research.

The application provides two main pages:
1. Epidemiology Research Interface - For conducting research with real-time progress tracking
2. Data Visualization Dashboard - For displaying and filtering extracted data
"""

from dotenv import load_dotenv
load_dotenv()

import streamlit as st
import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))


# Configure the Streamlit page
st.set_page_config(
    page_title="Epidemiology Research System",
    page_icon="🔬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Import page modules
from streamlit_app.pages.research_interface import research_interface_page
from streamlit_app.pages.data_dashboard import data_dashboard_page
from streamlit_app.utils.session_state import initialize_session_state
from streamlit_app.utils.database import check_database_connection, refresh_database_connection, clear_data_cache

def main():
    """Main application function with navigation and page routing."""
    
    # Initialize session state
    initialize_session_state()

    # Debug: Check environment variables
    if st.session_state.get("show_debug", False):
        with st.expander("🔧 Debug Info"):
            st.write("Environment Variables:")
            st.write(f"- MYSQL_HOST: {os.getenv('MYSQL_HOST')}")
            st.write(f"- MYSQL_PORT: {os.getenv('MYSQL_PORT')}")
            st.write(f"- MYSQL_DATABASE: {os.getenv('MYSQL_DATABASE')}")
            st.write(f"- MYSQL_USERNAME: {os.getenv('MYSQL_USERNAME')}")
            st.write(f"- MYSQL_PASSWORD: {'***' if os.getenv('MYSQL_PASSWORD') else 'NOT SET'}")

    # Check database connection on startup or when forced
    force_refresh = st.session_state.get("force_db_refresh", False)
    if "db_connection_checked" not in st.session_state or force_refresh:
        with st.spinner("Checking database connection..."):
            try:
                if force_refresh:
                    refresh_database_connection()
                    st.session_state.force_db_refresh = False

                db_status = check_database_connection()
                st.session_state.db_connection_checked = True
                st.session_state.db_connection_status = db_status
            except Exception as e:
                st.session_state.db_connection_checked = True
                st.session_state.db_connection_status = {"connected": False, "error": str(e)}

    # Display database connection status
    if not st.session_state.db_connection_status["connected"]:
        st.error(f"❌ Database Connection Failed: {st.session_state.db_connection_status['error']}")

        # Add refresh button
        col1, col2, col3 = st.columns([1, 1, 2])
        with col1:
            if st.button("🔄 Retry Connection", type="primary"):
                st.session_state.force_db_refresh = True
                st.rerun()

        with col2:
            if st.button("📊 Use Sample Data"):
                st.session_state.dashboard_filters["use_sample_data"] = True
                st.rerun()

        with col3:
            if st.button("🔧 Debug Info"):
                st.session_state.show_debug = not st.session_state.get("show_debug", False)
                st.rerun()

        st.info("Please check your database configuration in .env file or environment variables")
        st.info("Database should be: epidemiology_research with user: epidemiology_user")

        # Allow users to continue to dashboard even without database connection
        st.warning("⚠️ Some features may not work without database connection")

        # Show limited functionality message
        st.info("📊 **Limited Mode**: You can still access all features with sample data.")

        # Continue to show full navigation with sample data enabled
        st.session_state.dashboard_filters["use_sample_data"] = True
    else:
        # Only show success message briefly
        if not st.session_state.get("db_success_shown", False):
            st.success("✅ Database connection established")
            st.session_state.db_success_shown = True
    
    # Sidebar with system information and contextual controls
    st.sidebar.title("🔬 Epidemiology Research System")
    st.sidebar.markdown("---")

    # Display current session info in sidebar
    if "research_progress" in st.session_state and st.session_state.research_progress:
        st.sidebar.markdown("### 📈 Current Research")
        progress_info = st.session_state.research_progress
        st.sidebar.write(f"**Disease:** {progress_info.get('disease_name', 'N/A')}")
        st.sidebar.write(f"**Country:** {progress_info.get('country', 'N/A')}")
        st.sidebar.write(f"**Status:** {progress_info.get('current_step', 'Idle')}")

        if progress_info.get('is_running', False):
            st.sidebar.warning("🔄 Research in progress...")
        elif progress_info.get('current_step') == 'Completed':
            st.sidebar.success("✅ Research completed!")

    # Add system information
    st.sidebar.markdown("---")
    st.sidebar.markdown("### ℹ️ System Info")
    st.sidebar.caption(f"Database: {'✅ Connected' if st.session_state.db_connection_status['connected'] else '❌ Disconnected'}")
    st.sidebar.caption(f"Session: {st.session_state.get('session_id', 'Unknown')[:8]}...")

    # Add help section
    with st.sidebar.expander("❓ Help & Tips"):
        st.markdown("""
        **Getting Started:**
        1. Use Research Interface to analyze diseases
        2. View results in Data Dashboard
        3. Filter data by disease and country

        **Tips:**
        - Research runs synchronously for reliability
        - Dashboard supports sample data for testing
        - All progress is saved in session state
        """)

    # Initialize session ID if not exists
    if "session_id" not in st.session_state:
        import uuid
        st.session_state.session_id = str(uuid.uuid4())
    
    # Main content area with tab-based navigation
    st.title("🔬 Epidemiology Research System")
    st.markdown("Advanced tools for epidemiological research and data analysis")

    # Create main navigation tabs
    research_tab, dashboard_tab = st.tabs(["🔍 Research Interface", "📊 Data Dashboard"])

    with research_tab:
        st.markdown("### Conduct epidemiology research with real-time progress tracking")
        research_interface_page()

    with dashboard_tab:
        st.markdown("### Visualize and analyze extracted epidemiological data")
        # Add refresh data button for dashboard
        col1, col2, col3 = st.columns([1, 1, 2])
        with col1:
            if st.button("🔄 Refresh Data", help="Clear cache and reload data"):
                clear_data_cache()
                st.rerun()
        with col2:
            if st.button("📊 Use Sample Data", help="Switch to demo mode with sample data"):
                st.session_state.dashboard_filters["use_sample_data"] = True
                st.rerun()

        data_dashboard_page()

if __name__ == "__main__":
    main()
