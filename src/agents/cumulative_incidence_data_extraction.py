from dotenv import load_dotenv
load_dotenv()

# Add project root to Python path for imports
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import random
import logging
from textwrap import dedent
from typing import Dict, Any
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import json
from agno.tools.reasoning import ReasoningTools

from src.helpers.epidata_models import CumulativeIncidenceRecord, ExtractionSummary
from src.db import DatabaseConnection, ExtractionManager

# Set up logging
logger = logging.getLogger(__name__)

config = json.load(open(f"{project_root}/src/config.json"))

## 1) Cumulative Incidence Extraction Agent
cumulative_incidence_config = config["CumulativeIncidenceExtractionAgent"]

def record_cumulative_incidence_datapoints(session_state: Dict[str, Any], datapoint: CumulativeIncidenceRecord, confidence_value: float) -> str:
    """
    Record cumulative incidence datapoint from fulltext article.

    Args:
        session_state: Session state containing article and disease information
        datapoint: Pydantic model for Cumulative incidence datapoint
        confidence_value: Confidence value for the datapoint (0.0-1.0)

    Returns:
        str: Status message indicating whether datapoint was recorded
    """
    
    if confidence_value >= config["CumulativeIncidenceExtractionAgent"]["confidence_threshold"]:
        session_state["cumulative_incidence_datapoints"].append(datapoint)
        status = f"{len(session_state['cumulative_incidence_datapoints'])} cumulative incidence datapoints have been recorded"
    else:
        status = f"Cumulative incidence datapoint not recorded due to low confidence value ({confidence_value:.2f})"
    return status

def save_cumulative_incidence_datapoints(session_state: Dict[str, Any]) -> str:
    """
    Save all recorded cumulative incidence datapoints into the database.

    Args:
        session_state: Session state containing recorded datapoints and article metadata

    Returns:
        str: Status message indicating success or failure of save operation
    """
    try:
        db_connection = DatabaseConnection()
        db_connection.connect()
        extraction_manager = ExtractionManager(db_connection=db_connection)

        num_datapoints = len(session_state["cumulative_incidence_datapoints"])
        extraction_manager.save_cumulative_incidence_records(
            session_state["cumulative_incidence_datapoints"],
            session_state["pmid"],
            session_state["disease_name"],
            session_state["pmcid"]
        )
        db_connection.close()
        status = f"{num_datapoints} Cumulative incidence datapoints have been saved to the database"
    except Exception as e: 
        status = f"Failed to save cumulative incidence datapoints to the database: {str(e)}"
    return status

def get_cumulative_incidence_extraction_instructions() -> str:
    """
    Get instructions for cumulative incidence extraction agent.

    Returns:
        str: Detailed instructions for extracting cumulative incidence data
    """
    return dedent(
        """
        You are an expert epidemiologist and medical researcher specializing in extracting cumulative incidence data from scientific articles. Your task is to systematically analyze epidemiological literature and extract structured cumulative incidence datapoints.

        ## OVERVIEW
        Cumulative incidence (also called "incidence proportion" or "attack rate") represents the proportion of a disease-free population that develops the condition over a specified time period. It is expressed as a percentage and represents RISK, not a rate. Unlike incidence rate (which uses person-time), cumulative incidence is calculated as (new cases / population at risk at start) × 100. You will extract these datapoints using a structured approach with the available tools.

        ## TOOL USAGE INSTRUCTIONS

        ### 1. REASONING TOOLS
        Use the ReasoningTools throughout your analysis:

        **think()**: Use this to:
        - Plan your approach before reading the article
        - Break down complex sections of the article
        - Consider multiple interpretations of data
        - Reflect on extracted information before recording
        - Distinguish between cumulative incidence and other epidemiological measures

        **analyze()**: Use this to:
        - Examine statistical data and methodology sections
        - Evaluate data quality and reliability
        - Compare different cumulative incidence estimates within the same study
        - Assess demographic stratifications and subgroup analyses
        - Calculate cumulative incidence when not explicitly provided

        ### 2. DATA EXTRACTION WORKFLOW

        **STEP 1: Initial Analysis**
        - Use think() to plan your extraction strategy
        - Use analyze() to understand the study design, population, and methodology
        - Identify relevant sections (Results, Tables, Figures, Discussion)
        - Focus on cohort studies, clinical trials, and prospective studies

        **STEP 2: Systematic Data Identification**
        For each potential datapoint, use analyze() to evaluate:
        - Is this truly cumulative incidence (proportion of new cases over time)?
        - Are the cases incident (new) rather than prevalent (existing)?
        - Is the measurement expressed as a percentage or proportion?
        - Are the numerator (new cases) and denominator (at-risk population) clearly defined?
        - What population demographics are specified?
        - Is the time period clearly defined with start and end points?
        - Is the follow-up complete or are there significant losses?

        **STEP 3: Data Recording**
        Use record_cumulative_incidence_datapoints() for each valid datapoint with:
        - Complete CumulativeIncidenceRecord with all available fields
        - Confidence value between 0.0-1.0 based on data quality
        - Higher confidence (0.8-1.0) for: clear methodology, complete follow-up, well-defined populations
        - Lower confidence (0.5-0.7) for: unclear definitions, incomplete follow-up, limited demographic data

        **STEP 4: Final Database Save**
        Use save_cumulative_incidence_datapoints() once at the end to persist all extracted data.

        ### 3. CUMULATIVE INCIDENCE RECORD STRUCTURE

        Extract the following fields for each datapoint:

        **Required Fields:**
        - condition: Disease/condition name (should match the target disease)
        - cumulative_incidence_percent: Risk as percentage (calculated as (n_new_cases / n_at_risk_start) × 100)
        - start_year: Beginning year of the observation period
        - end_year: Ending year of the observation period

        **Optional but Important Fields:**
        - ci_lower_percent, ci_upper_percent: 95% confidence intervals as percentages
        - n_new_cases: Number of incident (new) cases during the period
        - n_at_risk_start: Number of disease-free individuals at the start of follow-up
        - country: ISO code (preferred) or country name
        - age_group: Format as 'all', 'X-Y', '>=X', or '<X'
        - gender: 'male', 'female', 'both', 'other', or 'unknown'
        - ethnicity: Specific ethnicity or 'all'/'unknown'

        ### 4. DATA QUALITY ASSESSMENT

        **High Confidence (0.8-1.0):**
        - Clear cumulative incidence definition and calculation
        - Complete or near-complete follow-up (>90%)
        - Well-defined observation period and population
        - Complete demographic information
        - Peer-reviewed source with clear methodology
        - Clear distinction between incident and prevalent cases
        - Large study population (>1,000 participants)

        **Medium Confidence (0.6-0.7):**
        - Generally clear but some ambiguity in definitions
        - Moderate follow-up completeness (70-90%)
        - Some missing demographic details
        - Limited methodological description
        - Medium study population (100-1,000 participants)
        - Some uncertainty about case definitions

        **Low Confidence (<0.6):**
        - Unclear cumulative incidence definition
        - Poor follow-up completeness (<70%)
        - Significant missing information
        - Questionable methodology
        - Small study population (<100 participants)
        - Possible confusion with prevalence or other measures

        ### 5. EXTRACTION GUIDELINES

        **DO Extract:**
        - Cohort study cumulative incidence
        - Clinical trial cumulative incidence for outcomes
        - Prospective study cumulative incidence
        - Surveillance-based cumulative incidence over defined periods
        - Subgroup analyses by age, gender, ethnicity, geography
        - Life-table cumulative incidence
        - Kaplan-Meier derived cumulative incidence

        **DON'T Extract:**
        - Prevalence estimates (existing cases at a point in time)
        - Incidence rates (per person-time measures)
        - Case-fatality rates
        - Mortality rates
        - Attack rates from very short outbreak periods (<1 month)
        - Cumulative incidence without clear time periods
        - Projected or modeled estimates without empirical data

        **Multiple Datapoints:**
        - Extract separate records for different demographic subgroups
        - Extract separate records for different time periods
        - Extract separate records for different geographic regions
        - Extract separate records for different follow-up durations

        ### 6. SYSTEMATIC APPROACH

        1. **Initialize**: Use think() to outline your extraction plan
        2. **Scan**: Use analyze() to identify all potential cumulative incidence data in tables, figures, and text
        3. **Evaluate**: For each potential datapoint, use think() to assess if it meets cumulative incidence criteria
        4. **Calculate**: Use analyze() to verify or calculate percentages when needed
        5. **Extract**: Use analyze() to carefully extract all relevant values and metadata
        6. **Record**: Use record_cumulative_incidence_datapoints() with appropriate confidence scores
        7. **Validate**: Use think() to review all extracted datapoints for completeness and accuracy
        8. **Save**: Use save_cumulative_incidence_datapoints() to persist all data
        9. **Summarize**: State the total number of cumulative incidence datapoints extracted and successfully saved to the database.

        ### 7. COMMON PITFALLS TO AVOID

        - Don't extract prevalence as cumulative incidence
        - Don't extract incidence rates (per person-time) as cumulative incidence
        - Don't record the same datapoint multiple times
        - Don't extract cumulative incidence from cross-sectional studies
        - Don't assume demographic characteristics if not explicitly stated
        - Don't extract cumulative incidence estimates that are clearly projections for future years
        - Don't confuse attack rates from short outbreaks with longer-term cumulative incidence
        - Don't extract survival probabilities as cumulative incidence (they are complements)

        ### 8. CALCULATION GUIDELINES

        **Standard Formula**: Cumulative Incidence (%) = (Number of new cases during period / Number at risk at start of period) × 100

        **Key Considerations**: 
        - Denominator must be disease-free at baseline
        - Numerator counts only NEW cases during follow-up
        - Time period must be clearly defined
        - Account for losses to follow-up when possible

        **Conversion Guidelines**:
        - If given as proportion (0.0-1.0): multiply by 100 to get percentage
        - If given per 1,000: divide by 10 to get percentage
        - If given per 10,000: divide by 100 to get percentage
        - Always express final result as percentage (0-100%)

        **Life Table Methods**:
        - Extract cumulative incidence at specific time points
        - Consider competing risks when mentioned
        - Use actuarial estimates when available

        ## FINAL REMINDERS

        - Use reasoning tools extensively to ensure accuracy
        - Extract only cumulative incidence (not other epidemiological measures)
        - Record each unique datapoint separately
        - Assign appropriate confidence scores based on study quality
        - Save all datapoints once at the end
        - Focus on the target disease but be aware of related conditions
        - Prioritize recent, high-quality studies with complete follow-up
        - Always verify that extracted data represents risk (proportion) over time, not rates
        - Pay attention to competing risks and loss to follow-up
        """
    )

def extract_cumulative_incidence_from_article(disease_name: str, pmcid: str, pmid: str) -> Dict[str, Any]:
    """
    Extract cumulative incidence datapoints from a full article markdown file.

    Args:
        disease_name: Name of the disease/condition of interest
        pmcid: PubMed Central ID of the article
        pmid: PubMed ID of the article

    Returns:
        Dict[str, Any]: Status object with extraction results and metadata
    """

    pmcid_number = pmcid.split("/")[-1]
    # Ensure PMC prefix is included in filename
    if not pmcid_number.startswith("PMC"):
        pmcid_number = f"PMC{pmcid_number}"
    with open(f"{project_root}/data/publications/{pmcid_number}.md", "r") as f:
        fulltext_article = f.read()

    tools = []
    if cumulative_incidence_config["use_reasoning"]:
        tools.append(ReasoningTools(enable_think=True, enable_analyze=True))
    tools.append(record_cumulative_incidence_datapoints)
    tools.append(save_cumulative_incidence_datapoints)

    agent = Agent(
        name="Cumulative Incidence Extraction Agent",
        model=OpenAIChat(id=cumulative_incidence_config["model_id"]),
        description="Analyze, record and save cumulative incidence datapoints into epidemiology research database from a full article markdown file of epidemiology article.",
        tools=tools,
        instructions=get_cumulative_incidence_extraction_instructions(),
        delay_between_retries=random.randint(1, 3),
        exponential_backoff=True,
        debug_mode=cumulative_incidence_config["debug_mode"],
        telemetry=False
    )
    
    status_object = {
        "disease": disease_name,
        "PMID": pmid,
        "PMCID": pmcid,
        "task": "cumulative_incidence_extraction",
        "status": "RUNNING",
        "extracted_datapoints": 0,
        "summary": ""
    }
    
    # Initialize database connection for updating extraction status
    db_connection = DatabaseConnection()
    extraction_manager = None
    try:
        db_connection.connect()
        extraction_manager = ExtractionManager(db_connection)
        
        # Update status to "running" when starting extraction
        extraction_manager.update_extraction_status(
            pmid=pmid,
            pmcid=pmcid,
            task_type="cumulative_incidence_extraction",
            status="running"
        )
        status_object['status'] = "RUNNING"
        
    except Exception as e:
        logger.warning(f"Could not update extraction status to running: {e}")
    
    try:
        current_session = {"pmid": pmid, "pmcid": pmcid, "disease_name": disease_name, "cumulative_incidence_datapoints":[]}
        response = agent.run(dedent(f"""
            Read the attached fulltext epidemiology article about {disease_name}. Think step-by-step and analyze the article for carefully extracting cumulative incidence datapoints.

            FULLTEXT ARTICLE:
            {fulltext_article}
            """),
            session_state=current_session
        )
        
        if isinstance(response.content, str):
            status_object['status'] = "SUCCESS"
            status_object['extracted_datapoints'] = len(current_session['cumulative_incidence_datapoints'])
            status_object['summary'] = response.content
        else:
            status_object['status'] = "FAILED"
            status_object['extracted_datapoints'] = 0
            status_object['summary'] = f"FAILED: Cumulative Incidence Extraction Error Type: {type(response)}"
    except Exception as e:
        status_object['status'] = "FAILED"
        status_object['extracted_datapoints'] = 0
        status_object['summary'] = f"FAILED: Exception during cumulative incidence extraction from article - {e}"
    finally:
        # Update dim_extraction table with final results
        if extraction_manager:
            try:
                extraction_manager.update_extraction_from_response(status_object)
                logger.debug(f"Updated dim_extraction table for PMID {pmid}")
            except Exception as e:
                logger.warning(f"Failed to update dim_extraction table: {e}")
        
        # Close database connection
        if db_connection and db_connection.engine:
            db_connection.close()
    
    return status_object
    

if __name__ == "__main__":
    # example usage
    pmid = "PMC4068111"
    pmcid = "PMC4068111"
    disease_name = "Diabetes Mellitus"
    extract_cumulative_incidence_from_article(disease_name, pmcid, pmid)
