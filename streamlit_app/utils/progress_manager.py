"""
Progress Manager for Background Thread Communication

This module provides a thread-safe way to communicate progress updates
from background threads to the main Streamlit thread.
"""

import threading
import time
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class ProgressState:
    """Thread-safe progress state container."""
    is_running: bool = False
    current_step: str = "Idle"
    disease_name: Optional[str] = None
    country: Optional[str] = None
    total_steps: int = 0
    completed_steps: int = 0
    step_details: Dict[str, Any] = field(default_factory=dict)
    results: Dict[str, Any] = field(default_factory=dict)
    errors: list = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.now)

class ProgressManager:
    """Thread-safe progress manager for background research pipeline."""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._progress = ProgressState()
        self._operation_status = {
            "screening": {"total": 0, "completed": 0, "status": "idle"},
            "download": {"total": 0, "completed": 0, "status": "idle"},
            "extraction": {"total": 0, "completed": 0, "status": "idle"}
        }
    
    def reset_progress(self):
        """Reset progress to initial state."""
        with self._lock:
            self._progress = ProgressState()
            self._operation_status = {
                "screening": {"total": 0, "completed": 0, "status": "idle"},
                "download": {"total": 0, "completed": 0, "status": "idle"},
                "extraction": {"total": 0, "completed": 0, "status": "idle"}
            }
    
    def set_running(self, disease_name: str, country: str, total_steps: int = 6):
        """Set research as running with initial parameters."""
        with self._lock:
            self._progress.is_running = True
            self._progress.disease_name = disease_name
            self._progress.country = country
            self._progress.total_steps = total_steps
            self._progress.completed_steps = 0
            self._progress.current_step = "Starting research..."
            self._progress.step_details = {}
            self._progress.results = {}
            self._progress.errors = []
            self._progress.last_updated = datetime.now()
    
    def set_completed(self):
        """Mark research as completed."""
        with self._lock:
            self._progress.is_running = False
            self._progress.current_step = "Completed"
            self._progress.last_updated = datetime.now()
    
    def update_progress(self, step: str, details: Optional[Dict[str, Any]] = None, increment_completed: bool = False):
        """Update research progress with current step and details."""
        with self._lock:
            self._progress.current_step = step
            self._progress.last_updated = datetime.now()
            
            if details:
                self._progress.step_details[step] = details
            
            if increment_completed:
                self._progress.completed_steps += 1
    
    def add_error(self, error: str):
        """Add an error to the research progress."""
        with self._lock:
            self._progress.errors.append(error)
            self._progress.last_updated = datetime.now()
            logger.error(f"Research pipeline error: {error}")
    
    def add_result(self, step: str, result: Any):
        """Add a result for a specific step."""
        with self._lock:
            self._progress.results[step] = result
            self._progress.last_updated = datetime.now()
    
    def update_operation_status(self, operation: str, total: Optional[int] = None, 
                              completed: Optional[int] = None, status: Optional[str] = None):
        """Update status of a specific operation."""
        with self._lock:
            if operation in self._operation_status:
                if total is not None:
                    self._operation_status[operation]["total"] = total
                if completed is not None:
                    self._operation_status[operation]["completed"] = completed
                if status is not None:
                    self._operation_status[operation]["status"] = status
    
    def get_progress(self) -> Dict[str, Any]:
        """Get current progress state as dictionary."""
        with self._lock:
            return {
                "is_running": self._progress.is_running,
                "current_step": self._progress.current_step,
                "disease_name": self._progress.disease_name,
                "country": self._progress.country,
                "total_steps": self._progress.total_steps,
                "completed_steps": self._progress.completed_steps,
                "step_details": self._progress.step_details.copy(),
                "results": self._progress.results.copy(),
                "errors": self._progress.errors.copy(),
                "last_updated": self._progress.last_updated
            }
    
    def get_operation_status(self, operation: str) -> Dict[str, Any]:
        """Get status of a specific operation."""
        with self._lock:
            return self._operation_status.get(operation, {"total": 0, "completed": 0, "status": "idle"}).copy()
    
    def get_all_operation_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all operations."""
        with self._lock:
            return {k: v.copy() for k, v in self._operation_status.items()}

# Global progress manager instance
_progress_manager = None
_manager_lock = threading.Lock()

def get_progress_manager() -> ProgressManager:
    """Get the global progress manager instance."""
    global _progress_manager
    with _manager_lock:
        if _progress_manager is None:
            _progress_manager = ProgressManager()
        return _progress_manager

def sync_progress_to_session_state(session_state):
    """Sync progress manager state to Streamlit session state with error handling."""
    try:
        manager = get_progress_manager()
        progress = manager.get_progress()
        operation_status = manager.get_all_operation_status()

        # Validate progress data before updating session state
        if not isinstance(progress, dict):
            logger.error(f"Invalid progress data type: {type(progress)}")
            return False

        if not isinstance(operation_status, dict):
            logger.error(f"Invalid operation status data type: {type(operation_status)}")
            return False

        # Ensure session state has the required structure
        if not hasattr(session_state, 'research_progress') or not isinstance(session_state.research_progress, dict):
            session_state.research_progress = {
                "is_running": False,
                "current_step": "Idle",
                "disease_name": None,
                "country": None,
                "total_steps": 0,
                "completed_steps": 0,
                "step_details": {},
                "results": {},
                "errors": []
            }

        if not hasattr(session_state, 'operation_status') or not isinstance(session_state.operation_status, dict):
            session_state.operation_status = {
                "screening": {"total": 0, "completed": 0, "status": "idle"},
                "download": {"total": 0, "completed": 0, "status": "idle"},
                "extraction": {"total": 0, "completed": 0, "status": "idle"}
            }

        # Update session state safely
        session_state.research_progress.update(progress)
        session_state.operation_status.update(operation_status)

        return True

    except Exception as e:
        logger.error(f"Error syncing progress to session state: {e}")
        return False

def sync_progress_from_session_state(session_state):
    """Sync Streamlit session state to progress manager."""
    manager = get_progress_manager()
    
    # Get current session state
    progress = session_state.research_progress
    
    # Update progress manager
    with manager._lock:
        manager._progress.is_running = progress.get("is_running", False)
        manager._progress.current_step = progress.get("current_step", "Idle")
        manager._progress.disease_name = progress.get("disease_name")
        manager._progress.country = progress.get("country")
        manager._progress.total_steps = progress.get("total_steps", 0)
        manager._progress.completed_steps = progress.get("completed_steps", 0)
        manager._progress.step_details = progress.get("step_details", {})
        manager._progress.results = progress.get("results", {})
        manager._progress.errors = progress.get("errors", [])
