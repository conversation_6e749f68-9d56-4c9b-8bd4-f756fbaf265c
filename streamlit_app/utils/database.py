"""
Database Utilities for Streamlit Application

This module provides database connection management and data retrieval functions
specifically designed for the Streamlit web interface.
"""

from dotenv import load_dotenv
# Load environment variables
load_dotenv()

import streamlit as st
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime
import os

# Import database components
from src.db import DatabaseConnection, ArticleManager, ExtractionManager
from src.db.models import (
    FactPointPrevalence, 
    FactPeriodPrevalence, 
    FactIncidenceRate, 
    FactCumulativeIncidence,
    DimArticles
)
from sqlalchemy import text, distinct

logger = logging.getLogger(__name__)

@st.cache_resource(ttl=300)  # Cache for 5 minutes
def get_database_connection():
    """Get a cached database connection for the Streamlit app."""
    try:
        # Ensure environment variables are loaded
        load_dotenv()

        db_connection = DatabaseConnection()
        db_connection.connect()
        db_connection.create_tables()

        # Test the connection
        with db_connection.get_session() as session:
            session.execute(text("SELECT 1"))

        logger.info("Database connection established successfully")
        return db_connection
    except Exception as e:
        logger.error(f"Failed to establish database connection: {e}")
        return None

def refresh_database_connection():
    """Force refresh the database connection by clearing cache."""
    try:
        get_database_connection.clear()
        logger.info("Database connection cache cleared")
    except Exception as e:
        logger.error(f"Error clearing database connection cache: {e}")

def check_database_connection() -> Dict[str, Any]:
    """Check database connection status."""
    try:
        # Check if environment variables are loaded
        mysql_host = os.getenv("MYSQL_HOST")
        mysql_database = os.getenv("MYSQL_DATABASE")
        mysql_username = os.getenv("MYSQL_USERNAME")
        mysql_password = os.getenv("MYSQL_PASSWORD")

        if not mysql_password:
            return {
                "connected": False,
                "error": "MYSQL_PASSWORD environment variable not found. Please check your .env file."
            }

        # Log connection attempt (without password)
        logger.info(f"Attempting database connection to {mysql_host}:{os.getenv('MYSQL_PORT', '3306')}/{mysql_database} as {mysql_username}")

        db_connection = get_database_connection()
        if db_connection is None:
            return {"connected": False, "error": "Failed to create database connection"}

        # Test the connection with a simple query
        with db_connection.get_session() as session:
            session.execute(text("SELECT 1"))

        logger.info("Database connection successful")
        return {"connected": True, "error": None}
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Database connection check failed: {error_msg}")

        # Provide more specific error messages
        if "Access denied" in error_msg:
            return {"connected": False, "error": "Access denied. Please check your username and password in .env file."}
        elif "Can't connect to MySQL server" in error_msg:
            return {"connected": False, "error": "Cannot connect to MySQL server. Please check if MySQL is running and the host/port are correct."}
        elif "Unknown database" in error_msg:
            return {"connected": False, "error": "Database does not exist. Please create the database or check the MYSQL_DATABASE setting."}
        else:
            return {"connected": False, "error": f"Database connection failed: {error_msg}"}

def get_article_manager():
    """Get an ArticleManager instance."""
    db_connection = get_database_connection()
    if db_connection:
        return ArticleManager(db_connection)
    return None

def get_extraction_manager():
    """Get an ExtractionManager instance."""
    db_connection = get_database_connection()
    if db_connection:
        return ExtractionManager(db_connection)
    return None

def get_article_manager_for_thread():
    """Get an ArticleManager instance for use in background threads."""
    try:
        # Create a new database connection without caching for thread safety
        db_connection = DatabaseConnection()
        db_connection.connect()
        db_connection.create_tables()
        return ArticleManager(db_connection)
    except Exception as e:
        logger.error(f"Failed to create ArticleManager for thread: {e}")
        return None

def get_extraction_manager_for_thread():
    """Get an ExtractionManager instance for use in background threads."""
    try:
        # Create a new database connection without caching for thread safety
        db_connection = DatabaseConnection()
        db_connection.connect()
        db_connection.create_tables()
        return ExtractionManager(db_connection)
    except Exception as e:
        logger.error(f"Failed to create ExtractionManager for thread: {e}")
        return None

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_available_diseases() -> List[str]:
    """Get list of unique diseases from the database."""
    try:
        db_connection = get_database_connection()
        if not db_connection:
            return []
        
        with db_connection.get_session() as session:
            # Get diseases from articles table
            diseases = session.query(distinct(DimArticles.disease_name)).all()
            return sorted([disease[0] for disease in diseases if disease[0]])
    except Exception as e:
        logger.error(f"Error fetching diseases: {e}")
        return []

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_available_countries() -> List[str]:
    """Get list of unique countries from the fact tables."""
    try:
        db_connection = get_database_connection()
        if not db_connection:
            return []
        
        countries = set()
        with db_connection.get_session() as session:
            # Get countries from all fact tables
            for model in [FactPointPrevalence, FactPeriodPrevalence, FactIncidenceRate, FactCumulativeIncidence]:
                country_results = session.query(distinct(model.country)).filter(model.country.isnot(None)).all()
                countries.update([country[0] for country in country_results if country[0]])
        
        return sorted(list(countries))
    except Exception as e:
        logger.error(f"Error fetching countries: {e}")
        return []

@st.cache_data(ttl=60)  # Cache for 1 minute
def get_prevalence_data(diseases: Optional[List[str]] = None, countries: Optional[List[str]] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Get point and period prevalence data with optional filtering."""
    try:
        db_connection = get_database_connection()
        if not db_connection:
            return pd.DataFrame(), pd.DataFrame()
        
        with db_connection.get_session() as session:
            # Point prevalence query
            point_query = session.query(FactPointPrevalence)
            if diseases:
                point_query = point_query.filter(FactPointPrevalence.disease_name.in_(diseases))
            if countries:
                point_query = point_query.filter(FactPointPrevalence.country.in_(countries))
            
            point_df = pd.read_sql(point_query.statement, session.bind) if session.bind else pd.DataFrame()
            
            # Period prevalence query
            period_query = session.query(FactPeriodPrevalence)
            if diseases:
                period_query = period_query.filter(FactPeriodPrevalence.disease_name.in_(diseases))
            if countries:
                period_query = period_query.filter(FactPeriodPrevalence.country.in_(countries))
            
            period_df = pd.read_sql(period_query.statement, session.bind) if session.bind else pd.DataFrame()
            
        return point_df, period_df
    except Exception as e:
        logger.error(f"Error fetching prevalence data: {e}")
        return pd.DataFrame(), pd.DataFrame()

@st.cache_data(ttl=60)  # Cache for 1 minute
def get_incidence_data(diseases: Optional[List[str]] = None, countries: Optional[List[str]] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Get incidence rate and cumulative incidence data with optional filtering."""
    try:
        db_connection = get_database_connection()
        if not db_connection:
            return pd.DataFrame(), pd.DataFrame()
        
        with db_connection.get_session() as session:
            # Incidence rate query
            rate_query = session.query(FactIncidenceRate)
            if diseases:
                rate_query = rate_query.filter(FactIncidenceRate.disease_name.in_(diseases))
            if countries:
                rate_query = rate_query.filter(FactIncidenceRate.country.in_(countries))
            
            rate_df = pd.read_sql(rate_query.statement, session.bind) if session.bind else pd.DataFrame()
            
            # Cumulative incidence query
            cumulative_query = session.query(FactCumulativeIncidence)
            if diseases:
                cumulative_query = cumulative_query.filter(FactCumulativeIncidence.disease_name.in_(diseases))
            if countries:
                cumulative_query = cumulative_query.filter(FactCumulativeIncidence.country.in_(countries))
            
            cumulative_df = pd.read_sql(cumulative_query.statement, session.bind) if session.bind else pd.DataFrame()
            
        return rate_df, cumulative_df
    except Exception as e:
        logger.error(f"Error fetching incidence data: {e}")
        return pd.DataFrame(), pd.DataFrame()

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_data_summary_stats() -> Dict[str, int]:
    """Get summary statistics for all data types."""
    try:
        db_connection = get_database_connection()
        if not db_connection:
            return {}
        
        stats = {}
        with db_connection.get_session() as session:
            stats['point_prevalence'] = session.query(FactPointPrevalence).count()
            stats['period_prevalence'] = session.query(FactPeriodPrevalence).count()
            stats['incidence_rate'] = session.query(FactIncidenceRate).count()
            stats['cumulative_incidence'] = session.query(FactCumulativeIncidence).count()
            stats['total_articles'] = session.query(DimArticles).count()
        
        return stats
    except Exception as e:
        logger.error(f"Error fetching summary stats: {e}")
        return {}

def clear_data_cache():
    """Clear all cached data to force refresh."""
    get_available_diseases.clear()
    get_available_countries.clear()
    get_prevalence_data.clear()
    get_incidence_data.clear()
    get_data_summary_stats.clear()
