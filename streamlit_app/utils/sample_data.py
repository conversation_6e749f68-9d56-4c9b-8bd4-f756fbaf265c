"""
Sample Data Generator for Testing

This module provides sample data for testing the dashboard when no real data is available.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import <PERSON>ple

def generate_sample_prevalence_data() -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Generate sample point and period prevalence data for testing."""
    
    # Sample diseases and countries
    diseases = ["Type 2 Diabetes", "Hypertension", "Ulcerative Colitis", "Crohn's Disease", "Asthma"]
    countries = ["US", "UK", "CA", "AU", "DE", "FR", "JP", "IN"]
    
    # Generate point prevalence data
    point_data = []
    for i in range(100):
        disease = np.random.choice(diseases)
        country = np.random.choice(countries)
        year = np.random.randint(2010, 2024)
        prevalence = np.random.uniform(0.5, 15.0)  # 0.5% to 15%
        n_cases = np.random.randint(100, 10000)
        n_population = int(n_cases / (prevalence / 100))
        
        point_data.append({
            'id': i + 1,
            'pmid': f"PMID{i+1000}",
            'disease_name': disease,
            'pmcid': f"PMC{i+2000}",
            'condition': disease,
            'point_prevalence_percent': round(prevalence, 2),
            'ci_lower_percent': round(max(0, prevalence - np.random.uniform(0.5, 2.0)), 2),
            'ci_upper_percent': round(prevalence + np.random.uniform(0.5, 2.0), 2),
            'n_cases': n_cases,
            'n_population': n_population,
            'year': year,
            'country': country,
            'age_group': np.random.choice(['18-30', '31-50', '51-70', '70+', 'All ages']),
            'gender': np.random.choice(['male', 'female', 'both']),
            'ethnicity': np.random.choice(['Caucasian', 'Asian', 'Hispanic', 'African American', 'Mixed', 'Not specified']),
            'created_at': datetime.now() - timedelta(days=np.random.randint(1, 365)),
            'updated_at': datetime.now()
        })
    
    point_df = pd.DataFrame(point_data)
    
    # Generate period prevalence data
    period_data = []
    for i in range(80):
        disease = np.random.choice(diseases)
        country = np.random.choice(countries)
        start_year = np.random.randint(2010, 2020)
        end_year = start_year + np.random.randint(1, 5)
        prevalence = np.random.uniform(1.0, 20.0)  # 1% to 20%
        n_cases = np.random.randint(200, 15000)
        n_population = int(n_cases / (prevalence / 100))
        
        period_data.append({
            'id': i + 1,
            'pmid': f"PMID{i+2000}",
            'disease_name': disease,
            'pmcid': f"PMC{i+3000}",
            'condition': disease,
            'period_prevalence_percent': round(prevalence, 2),
            'ci_lower_percent': round(max(0, prevalence - np.random.uniform(0.5, 3.0)), 2),
            'ci_upper_percent': round(prevalence + np.random.uniform(0.5, 3.0), 2),
            'n_cases': n_cases,
            'n_population': n_population,
            'start_year': start_year,
            'end_year': end_year,
            'country': country,
            'age_group': np.random.choice(['18-30', '31-50', '51-70', '70+', 'All ages']),
            'gender': np.random.choice(['male', 'female', 'both']),
            'ethnicity': np.random.choice(['Caucasian', 'Asian', 'Hispanic', 'African American', 'Mixed', 'Not specified']),
            'created_at': datetime.now() - timedelta(days=np.random.randint(1, 365)),
            'updated_at': datetime.now()
        })
    
    period_df = pd.DataFrame(period_data)
    
    return point_df, period_df

def generate_sample_incidence_data() -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Generate sample incidence rate and cumulative incidence data for testing."""
    
    diseases = ["Type 2 Diabetes", "Hypertension", "Ulcerative Colitis", "Crohn's Disease", "Asthma"]
    countries = ["US", "UK", "CA", "AU", "DE", "FR", "JP", "IN"]
    
    # Generate incidence rate data
    rate_data = []
    for i in range(60):
        disease = np.random.choice(diseases)
        country = np.random.choice(countries)
        start_year = np.random.randint(2010, 2020)
        end_year = start_year + np.random.randint(1, 5)
        incidence_rate = np.random.uniform(10, 500)  # per 100,000 person-years
        n_cases = np.random.randint(50, 5000)
        person_years = n_cases / (incidence_rate / 100000)
        
        rate_data.append({
            'id': i + 1,
            'pmid': f"PMID{i+3000}",
            'disease_name': disease,
            'pmcid': f"PMC{i+4000}",
            'condition': disease,
            'incidence_rate': round(incidence_rate, 2),
            'ci_lower': round(max(0, incidence_rate - np.random.uniform(5, 50)), 2),
            'ci_upper': round(incidence_rate + np.random.uniform(5, 50), 2),
            'n_cases': n_cases,
            'person_years': round(person_years, 1),
            'start_year': start_year,
            'end_year': end_year,
            'country': country,
            'age_group': np.random.choice(['18-30', '31-50', '51-70', '70+', 'All ages']),
            'gender': np.random.choice(['male', 'female', 'both']),
            'ethnicity': np.random.choice(['Caucasian', 'Asian', 'Hispanic', 'African American', 'Mixed', 'Not specified']),
            'created_at': datetime.now() - timedelta(days=np.random.randint(1, 365)),
            'updated_at': datetime.now()
        })
    
    rate_df = pd.DataFrame(rate_data)
    
    # Generate cumulative incidence data
    cumulative_data = []
    for i in range(50):
        disease = np.random.choice(diseases)
        country = np.random.choice(countries)
        start_year = np.random.randint(2010, 2020)
        end_year = start_year + np.random.randint(1, 5)
        cumulative_incidence = np.random.uniform(0.5, 10.0)  # 0.5% to 10%
        n_new_cases = np.random.randint(25, 2000)
        n_at_risk_start = int(n_new_cases / (cumulative_incidence / 100))
        
        cumulative_data.append({
            'id': i + 1,
            'pmid': f"PMID{i+4000}",
            'disease_name': disease,
            'pmcid': f"PMC{i+5000}",
            'condition': disease,
            'cumulative_incidence_percent': round(cumulative_incidence, 2),
            'ci_lower_percent': round(max(0, cumulative_incidence - np.random.uniform(0.2, 1.0)), 2),
            'ci_upper_percent': round(cumulative_incidence + np.random.uniform(0.2, 1.0), 2),
            'n_new_cases': n_new_cases,
            'n_at_risk_start': n_at_risk_start,
            'start_year': start_year,
            'end_year': end_year,
            'country': country,
            'age_group': np.random.choice(['18-30', '31-50', '51-70', '70+', 'All ages']),
            'gender': np.random.choice(['male', 'female', 'both']),
            'ethnicity': np.random.choice(['Caucasian', 'Asian', 'Hispanic', 'African American', 'Mixed', 'Not specified']),
            'created_at': datetime.now() - timedelta(days=np.random.randint(1, 365)),
            'updated_at': datetime.now()
        })
    
    cumulative_df = pd.DataFrame(cumulative_data)
    
    return rate_df, cumulative_df

def get_sample_summary_stats() -> dict:
    """Get sample summary statistics."""
    return {
        'point_prevalence': 100,
        'period_prevalence': 80,
        'incidence_rate': 60,
        'cumulative_incidence': 50,
        'total_articles': 150
    }

def get_sample_diseases() -> list:
    """Get sample disease list."""
    return ["Type 2 Diabetes", "Hypertension", "Ulcerative Colitis", "Crohn's Disease", "Asthma"]

def get_sample_countries() -> list:
    """Get sample country list."""
    return ["US", "UK", "CA", "AU", "DE", "FR", "JP", "IN"]
